import React, { useEffect, useRef, useState } from "react";
import DatepickerImage from "../../assets/images/Datepicker/Datepicker.png";
import Arrow from "../../assets/images/arrows/arrow.png";
import { ViewMode } from "../../models/Enums/ViewMode";
import Translator from "../../services/language/Translator";
import { monthsShort } from "../CalendarComponent/constants/Names";
import Container from "../Container";
import DateElement from "./DateElement";
import MonthsElement from "./MonthElement";
import {
  ArrowImage,
  ContainerDropDown,
  ContainerNavBar,
  DatePickerIcon,
  LabelCentered,
  MainContainer,
  StyledTextBox,
} from "./Styles";
import YearsElement from "./YearsElement";

interface DatepickerProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  initialDate?: Date | null;
  initialMonth?: number;
  initialYear?: number;
  onSelectDate: (date: Date) => void;
  label?: string;
  disabled?: boolean;
}

const Datepicker: React.FC<DatepickerProps> = ({
  initialDate,
  initialMonth,
  initialYear,
  onSelectDate,
  label,
  disabled,
}) => {
  const initialBaseDate = initialDate
    ? initialDate
    : initialYear !== undefined && initialMonth !== undefined
    ? new Date(initialYear, initialMonth, 1)
    : new Date();

  const [date, setDate] = useState(initialBaseDate);
  const [selectedDay, setSelectedDay] = useState(initialBaseDate.getDate());
  const [selectedMonth, setSelectedMonth] = useState(
    initialBaseDate.getMonth()
  );
  const [selectedYear, setSelectedYear] = useState(
    initialBaseDate.getFullYear()
  );
  const [selectedGroupIndex, setSelectedGroupIndex] = useState(10);
  const [datepickerView, setDatepickerView] = useState(ViewMode.DatesView);
  const [textInput, setTextInput] = useState("");
  const [isDatePickerActive, setDatePickerActive] = useState(false);
  const datepickerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!initialDate) {
      setTextInput("");
      const base =
        initialYear !== undefined && initialMonth !== undefined
          ? new Date(initialYear, initialMonth, 1)
          : new Date();
      setDate(base);
      setSelectedDay(base.getDate());
      setSelectedMonth(base.getMonth());
      setSelectedYear(base.getFullYear());
      return;
    }

    setDate(initialDate);
    setSelectedDay(initialDate.getDate());
    setSelectedMonth(initialDate.getMonth());
    setSelectedYear(initialDate.getFullYear());
    setTextInput(formatDate(initialDate));
  }, [initialDate, initialMonth, initialYear]);

  const handleDatePickerClick = () => {
    setDatePickerActive(!isDatePickerActive);
  };

  const handleMonthClick = () => {
    if (datepickerView === ViewMode.MonthsView) {
      setDatepickerView(ViewMode.DatesView);
    } else setDatepickerView(ViewMode.MonthsView);
  };

  const handleYearClick = () => {
    if (datepickerView === ViewMode.YearsView) {
      setDatepickerView(ViewMode.MonthsView);
    } else setDatepickerView(ViewMode.YearsView);
  };

  const goToNextMonth = () => {
    let newMonth = selectedMonth + 1;
    let newYear = selectedYear;

    if (newMonth > 11) {
      newMonth = 0;
      newYear += 1;
    }

    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const goToPrevMonth = () => {
    let newMonth = selectedMonth - 1;
    let newYear = selectedYear;

    if (newMonth < 0) {
      newMonth = 11;
      newYear -= 1;
    }

    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const goToNextYear = () => {
    if (datepickerView === ViewMode.YearsView) changeGroupIndex(1);
    else setSelectedYear(selectedYear + 1);
  };

  const changeGroupIndex = (number: number) => {
    setSelectedGroupIndex(selectedGroupIndex + number);
  };

  const goToPrevYear = () => {
    if (datepickerView === ViewMode.YearsView) changeGroupIndex(-1);
    else setSelectedYear(selectedYear - 1);
  };

  const handleDateClick = (day: number, index: number = 0) => {
    const newDate = new Date(selectedYear, selectedMonth + index, day);
    setDate(date);
    setSelectedDay(day);
    setSelectedMonth(selectedMonth + index);
    setSelectedYear(selectedYear);
    setTextInput(formatDate(newDate));
    onSelectDate(newDate);
    setDatePickerActive(false);
  };

  const handleMonthsClick = (selectedMonth: number) => {
    setSelectedMonth(selectedMonth);
    setSelectedYear(selectedYear);
    handleMonthClick();
  };

  const handleYearsClick = (selectedYear: number) => {
    setSelectedMonth(selectedMonth);
    setSelectedYear(selectedYear);
    handleYearClick();
  };

  const formatDate = (d: Date) => {
    const day = d.getDate() < 10 ? "0" + d.getDate() : d.getDate();
    const month = (d.getMonth() + 1 < 10 ? "0" : "") + (d.getMonth() + 1);
    const year = d.getFullYear();

    return `${day}/${month}/${year}`;
  };

  const mthElement = (
    <Container
      data-testid="selected-month"
      className="month"
      onClick={handleMonthClick}
    >
      <Translator getString={monthsShort[selectedMonth]} />
    </Container>
  );

  const yearElement = (
    <Container
      data-testid="selected-year"
      className="year"
      onClick={handleYearClick}
    >
      {selectedYear}
    </Container>
  );

  const assignInput = (inputValue: string, currentYear: number): string => {
    const currentMonth = (new Date().getMonth() + 1)
      .toString()
      .padStart(2, "0");

    let formattedValue = inputValue.replace(/\D/g, "");

    if (formattedValue.length > 0) {
      if (formattedValue.length < 2) {
        formattedValue = "0" + formattedValue;
      }
      if (formattedValue.length <= 2) {
        formattedValue += currentMonth;
      }
      if (formattedValue.length <= 4) {
        formattedValue = formattedValue.replace(
          /(\d{2})(\d{2})(\d{0,4})/,
          `$1/$2/${currentYear}$3`
        );
      } else if (formattedValue.length === 5) {
        formattedValue = formattedValue.replace(
          /(\d{2})(\d{2})(\d{0,4})/,
          `$1/$2/200$3`
        );
      } else if (formattedValue.length === 6) {
        formattedValue = formattedValue.replace(
          /(\d{2})(\d{2})(\d{0,4})/,
          `$1/$2/20$3`
        );
      } else if (formattedValue.length === 8) {
        formattedValue = formattedValue.replace(
          /(\d{2})(\d{2})(\d{0,4})/,
          `$1/$2/$3`
        );
      }
    }

    return formattedValue;
  };

  const daysInMonth = (month: number, year: number) => {
    return new Date(year, month, 0).getDate();
  };

  const formatInputValue = (inputValue: string) => {
    const currentYear = new Date().getFullYear();

    let formattedValue = assignInput(inputValue, currentYear);

    let day = parseInt(formattedValue.slice(0, 2));
    let month = parseInt(formattedValue.slice(3, 5));
    let year = parseInt(formattedValue.slice(6, 10));

    const amountDays = daysInMonth(month, year);

    if (month < 1) {
      formattedValue = formattedValue.replace(
        /(\d{2})\/(\d{2})\/(\d{0,4})/,
        `$1/01/$3`
      );
    } else if (month > 12) {
      month = 12;
      formattedValue = formattedValue.replace(
        /(\d{2})\/(\d{2})\/(\d{0,4})/,
        `$1/${month}/$3`
      );
    }

    if (day > amountDays) {
      formattedValue = formattedValue.replace(/(\d{2})/, `${amountDays}`);
    }

    if (year < 1) {
      formattedValue = formattedValue.replace(
        /(\d{2})\/(\d{2})\/(\d{0,4})/,
        `$1/$2/${currentYear}`
      );
    }

    return formattedValue;
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      const formattedValue = formatInputValue(textInput);

      const parts = formattedValue.split("/");

      if (parts.length == 3) {
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1;
        const year = parseInt(parts[2], 10);

        setSelectedDay(day);
        setSelectedMonth(month);
        setSelectedYear(year);

        setTextInput(formattedValue);

        const newDate = new Date(year, month, day);
        onSelectDate(newDate);
      }

      setDatepickerView(ViewMode.DatesView);
      setDatePickerActive(false);
    }
  };

  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTextInput(event.target.value);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        datepickerRef.current &&
        !datepickerRef.current.contains(event.target as Node)
      ) {
        setDatePickerActive(false);
        setDatepickerView(ViewMode.DatesView);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  return (
    <MainContainer
      ref={datepickerRef}
      isDatePickerActive={isDatePickerActive}
      data-testid="datepicker-main-container"
    >
      <StyledTextBox
        data-testid="datepicker-input"
        handleChange={handleDateChange}
        value={textInput}
        onKeyDown={handleKeyPress}
        type="text"
        label={label ?? "strGetInputDate"}
        placeholder={""}
        disabled={disabled}
      />
      <DatePickerIcon
        data-testid="datepicker-icon"
        src={DatepickerImage}
        onClick={handleDatePickerClick}
        size="small"
      ></DatePickerIcon>
      <ContainerDropDown
        active={isDatePickerActive}
        data-testid="datepicker-dropdown"
      >
        <ContainerNavBar data-testid="datepicker-navbar">
          <ArrowImage
            data-testid="datepicker-prev-button"
            src={Arrow}
            angle={270}
            left={1}
            top={1}
            onClick={
              datepickerView === ViewMode.YearsView
                ? goToPrevYear
                : goToPrevMonth
            }
          ></ArrowImage>
          <LabelCentered fontSize={1} data-testid="datepicker-year-label">
            {" "}
            {yearElement}
          </LabelCentered>
          <LabelCentered fontSize={1.2} data-testid="datepicker-month-label">
            {mthElement}
          </LabelCentered>
          <ArrowImage
            data-testid="datepicker-next-button"
            src={Arrow}
            angle={90}
            right={1}
            top={1}
            onClick={
              datepickerView === ViewMode.YearsView
                ? goToNextYear
                : goToNextMonth
            }
          ></ArrowImage>
        </ContainerNavBar>
        <DateElement
          data-testid="datepicker-date-element"
          active={datepickerView}
          selectedDay={selectedDay}
          selectedMonth={selectedMonth}
          selectedYear={selectedYear}
          handleDateClick={handleDateClick}
        />
        <MonthsElement
          data-testid="datepicker-months-element"
          active={datepickerView}
          handleMonthsClick={handleMonthsClick}
        />
        <YearsElement
          data-testid="datepicker-years-element"
          active={datepickerView}
          handleYearsClick={handleYearsClick}
          selectedGroupIndex={selectedGroupIndex}
        />
      </ContainerDropDown>
    </MainContainer>
  );
};

export default Datepicker;
