import { useEffect, useState } from "react";
import styled from "styled-components";
import profileMan from "../../../assets/images/profile/profileMan.svg";
import Container from "../../../components/Container";
import Fieldset from "../../../components/Fiedlset/Fieldset";
import Legend from "../../../components/Fiedlset/Legend";
import Image from "../../../components/Image";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";

import editIconHover from "../../../assets/images/profile/editHover.svg";
import editIcon from "../../../assets/images/profile/editNormal.svg";
import ApproveEditCardHoverIcon from "../../../components/ApproveEdit/ApproveEditCardHoverIcon";
import ViewEditCardHoverIcon from "../../../components/ApproveEdit/ViewEditCardHoverIcon";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import { ApproveEmployeeEditDTO } from "../../../models/DTOs/editEmployee/ApproveEmployeeEditDTO";
import { DeclineEmployeeEditDTO } from "../../../models/DTOs/editEmployee/DeclineEmployeeEditDTO";
import { PersonalInformationDTO } from "../../../models/DTOs/payrolls/PersonalInformationDTO";
import {
  approveEmployeeEdit,
  declineEmployeeEdit,
} from "../../../services/employees/employeesService";
import Translator from "../../../services/language/Translator";
import { useMenu } from "../../MenuContext";

const FieldsetRow = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  margin: 0.5rem 0;
  width: 100%;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  margin: 1rem;
  padding: 1rem;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

const EmployeeImage = styled(Image)`
  border-radius: 50%;
  margin: 0;
  height: 4rem;
  width: 4rem;
`;

const EmployeeName = styled(Label)`
  text-align: left;
  font-weight: bold;
  opacity: 1;
  font-size: 1rem;
  word-wrap: normal;
  margin: 0;
`;

const DepartmentName = styled(Label)`
  font-size: 1rem;
  text-align: left;
  margin: 0;
  color: var(--profile-department-name-font-color);
`;

const LightLabel = styled(Label)`
  color: var(--profile-department-leader-name-font-color);
  font-size: 0.9rem;
`;

const ValueLabel = styled(Label)`
  font-size: 1.2rem;
` as unknown as React.FC<{ children: React.ReactNode }>;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;
  flex: 1;
  height: 100%;

  @media (max-width: 1200px) {
    width: 100%;
    margin-left: 0;
    height: auto;
  }
`;

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0.5rem;
  margin: 0 auto;

  @media (max-width: 1200px) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const LabelColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 30%;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 70%;
`;

const EditButton = styled(Button)<{
  normalImage: string;
  hoverImage: string;
  isdisabled: boolean;
}>`
  position: absolute;
  top: -1rem;
  right: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  background-color: transparent;
  background: no-repeat center / 1.6rem url(${(p) => p.normalImage});
  cursor: pointer;
  padding: 0;

  &:hover {
    background: no-repeat center / 1.6rem url(${(p) => p.hoverImage});
  }
`;

const TopContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  gap: 1rem;
`;

const EmployeeInfoContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  justify-content: center;
`;

const RightColumn = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 17%;
  height: 100%;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;
  border-radius: 2.2rem;
  justify-content: flex-start;
  align-items: flex-start;
`;

const MenuText = styled(Label)<{ isSelected?: boolean }>`
  font-size: 0.9rem;
  margin-bottom: 0.2rem;
  text-align: left;
  color: ${(props) =>
    props.isSelected ? "var(--profile-menu-text-hover-color)" : "#bdc4d6"};
  cursor: pointer;
  width: 100%;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
`;

const MenuItem = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 0.5rem;
  padding-top: 0;
  align-items: flex-start;

  &:hover {
    ${MenuText} {
      color: var(--profile-department-name-font-color);
    }
  }
`;

const MenuLine = styled.div`
  width: 100%;
  height: 1px;
  background-color: #bdc4d6;
  opacity: 0.5;
`;

const ContainersWrapper = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 2rem;
  align-items: flex-start;
`;

const MainContentWrapper = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 85%;
  gap: 1rem;
`;

const MainContent = styled(Container)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  width: 100%;
  height: 100%;
  column-gap: 2rem;
  @media (max-width: 1200px) {
    display: flex;
    flex-direction: column;
    height: auto;
  }
`;

const RightContainersWrapper = styled(Container)`
  display: flex;
  height: 100%;
  flex-direction: column;
  flex: 1;

  @media (max-width: 1200px) {
    width: 100%;
    margin-left: 0;
    height: auto;
  }
`;

const RightContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;

  @media (max-width: 1200px) {
    width: 100%;
  }
`;

interface Props {
  profile: PersonalInformationDTO;
  employeeName: string;
  isApprover: boolean;
  shouldShowEditButtons: boolean;
}

const getAddressPurposeDescription = (purpose: number): string => {
  switch (purpose) {
    case AddressPurpose.IdentityCard:
      return "Identity card";
    case AddressPurpose.ForContact:
      return "For contact";
    case AddressPurpose.ForRemoteWork:
      return "For remote work";
    case AddressPurpose.Abroad:
      return "Abroad";
    case AddressPurpose.Custom:
      return "Custom";
    default:
      return "Unknown";
  }
};

const getAddressPurposeDescriptionGroupBox = (purpose: number): string => {
  switch (purpose) {
    case AddressPurpose.IdentityCard:
      return "Address identity card";
    case AddressPurpose.ForContact:
      return "Address for correspondence";
    case AddressPurpose.ForRemoteWork:
      return "Address for remote work";
    case AddressPurpose.Abroad:
      return "Address abroad";
    case AddressPurpose.Custom:
      return "Address custom";
    default:
      return "Address unknown";
  }
};

function formatFullAddress(address?: AddressDTO | null) {
  if (!address) return "";
  const parts = [];
  if (address.neighborhood) parts.push(`${address.neighborhood},`);
  if (address.street) parts.push(` ${address.street}`);
  if (address.block) parts.push(`, ${address.block}`);
  if (address.apartment) parts.push(`, ${address.apartment}`);
  return parts.join(" ");
}

const Addresses = ({
  profile,
  employeeName,
  isApprover,
  shouldShowEditButtons,
}: Props) => {
  const addressesList = profile.addresses;
  const employeeId = profile.employee.workTimeId;
  const payrollId = profile.payrollPersonalData.id;
  const [addresses, setAddresses] = useState<AddressDTO[] | null>();
  const [selectedAddress, setSelectedAddress] = useState<AddressDTO | null>();
  const [selectedPreviousAddress, setSelectedPreviousAddress] =
    useState<AddressDTO | null>();
  const [localProfile, setLocalProfile] = useState(profile);
  const { toggleMenu, changeView } = useMenu();

  useEffect(() => {
    setLocalProfile(profile);
  }, [profile]);

  useEffect(() => {
    var filteredAddresses = localProfile.addresses.filter(
      (address: AddressDTO) =>
        address.purpose &&
        address.purpose.identifier !== AddressPurpose.IdentityCard
    );
    setAddresses(filteredAddresses);
    setSelectedAddress(filteredAddresses[0]);

    if (
      localProfile?.employeePreviousValues?.addresses &&
      filteredAddresses[0]
    ) {
      const previousAddress =
        localProfile.employeePreviousValues.addresses.find(
          (addr: AddressDTO) =>
            addr.purpose?.identifier ===
            filteredAddresses[0].purpose?.identifier
        );
      setSelectedPreviousAddress(previousAddress || null);
    }
  }, [localProfile.addresses, localProfile?.employeePreviousValues?.addresses]);

  const handleAddressSelect = (address: AddressDTO) => {
    setSelectedAddress(address);

    if (localProfile?.employeePreviousValues?.addresses) {
      const previousAddress =
        localProfile.employeePreviousValues.addresses.find(
          (addr: AddressDTO) =>
            addr.purpose?.identifier === address.purpose?.identifier
        );
      setSelectedPreviousAddress(previousAddress || null);
    }
  };

  const handleAddNewAddress = () => {
    setSelectedAddress(null);
  };

  const handleEditAddress = () => {
    const step = selectedAddress?.purpose?.identifier ?? 1;
    changeView("edit-employee-addresses", "other", {
      addressesList,
      employeeId,
      payrollId,
      step,
    });
    toggleMenu();
  };

  return (
    <WrapperContainer>
      <ContainersWrapper>
        <MainContentWrapper>
          <TopContainer>
            <EmployeeImage
              src={profileMan}
              data-testid="profile-employee-image"
            />
            <EmployeeInfoContainer>
              <EmployeeName data-testid="profile-employee-name">
                {employeeName}
              </EmployeeName>
              <DepartmentName data-testid="profile-department-name">
                Department Name
              </DepartmentName>
            </EmployeeInfoContainer>
          </TopContainer>
          <MainContent>
            <LeftContainer data-testid="profile-left-container">
              <StyledFieldset
                onSubmit={(e) => e.preventDefault()}
                data-testid="current-address-fieldset"
              >
                <Legend data-testid="information-legend">
                  <Translator
                    getString={getAddressPurposeDescriptionGroupBox(
                      selectedAddress?.purpose?.identifier ?? 1
                    )}
                  />
                </Legend>
                <EditButton
                  data-testid="edit-address-button"
                  normalImage={editIcon}
                  hoverImage={editIconHover}
                  onClick={() => handleEditAddress()}
                  label=""
                  isdisabled={true}
                />
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>City</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>
                      {shouldShowEditButtons &&
                      (selectedPreviousAddress?.city?.name !==
                        selectedAddress?.city?.name ||
                        selectedPreviousAddress?.cityName !==
                          selectedAddress?.cityName) &&
                      isApprover
                        ? selectedPreviousAddress?.city?.name ??
                          selectedPreviousAddress?.cityName
                        : selectedAddress?.city?.name ??
                          selectedAddress?.cityName}
                    </ValueLabel>
                    {shouldShowEditButtons &&
                      (selectedPreviousAddress?.city?.name !==
                        selectedAddress?.city?.name ||
                        selectedPreviousAddress?.cityName !==
                          selectedAddress?.cityName) &&
                      (isApprover ? (
                        <ApproveEditCardHoverIcon
                          newValue={
                            selectedAddress?.city?.name ??
                            selectedAddress?.cityName
                          }
                          onConfirm={async () => {
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "CityId",
                              })
                            );
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "CityName",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              employeePreviousValues:
                                localProfile.employeePreviousValues
                                  ? {
                                      ...localProfile.employeePreviousValues,
                                      addresses:
                                        localProfile.employeePreviousValues.addresses?.map(
                                          (addr) =>
                                            addr.purpose?.identifier ===
                                            selectedAddress?.purpose?.identifier
                                              ? {
                                                  ...addr,
                                                  city: selectedAddress?.city,
                                                  cityName:
                                                    selectedAddress?.cityName,
                                                }
                                              : addr
                                        ),
                                    }
                                  : null,
                            });
                          }}
                          onCancel={async () => {
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "CityId",
                              })
                            );
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "CityName",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              addresses: localProfile.addresses.map((addr) =>
                                addr.purpose?.identifier ===
                                selectedAddress?.purpose?.identifier
                                  ? {
                                      ...addr,
                                      city: selectedPreviousAddress!.city,
                                      cityName:
                                        selectedPreviousAddress!.cityName,
                                    }
                                  : addr
                              ),
                            });
                          }}
                        />
                      ) : (
                        <ViewEditCardHoverIcon
                          previousValue={
                            selectedPreviousAddress?.city?.name ??
                            selectedPreviousAddress?.cityName
                          }
                        />
                      ))}
                  </ValueColumn>
                </FieldsetRow>
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>PC</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>
                      {shouldShowEditButtons &&
                      selectedPreviousAddress?.postalCode !==
                        selectedAddress?.postalCode &&
                      isApprover
                        ? selectedPreviousAddress?.postalCode
                        : selectedAddress?.postalCode}
                    </ValueLabel>
                    {shouldShowEditButtons &&
                      selectedPreviousAddress?.postalCode !==
                        selectedAddress?.postalCode &&
                      (isApprover ? (
                        <ApproveEditCardHoverIcon
                          newValue={selectedAddress?.postalCode}
                          onConfirm={async () => {
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "PostalCode",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              employeePreviousValues:
                                localProfile.employeePreviousValues
                                  ? {
                                      ...localProfile.employeePreviousValues,
                                      addresses:
                                        localProfile.employeePreviousValues.addresses?.map(
                                          (addr) =>
                                            addr.purpose?.identifier ===
                                            selectedAddress?.purpose?.identifier
                                              ? {
                                                  ...addr,
                                                  postalCode:
                                                    selectedAddress?.postalCode,
                                                }
                                              : addr
                                        ),
                                    }
                                  : null,
                            });
                          }}
                          onCancel={async () => {
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "PostalCode",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              addresses: localProfile.addresses.map((addr) =>
                                addr.purpose?.identifier ===
                                selectedAddress?.purpose?.identifier
                                  ? {
                                      ...addr,
                                      postalCode:
                                        selectedPreviousAddress!.postalCode,
                                    }
                                  : addr
                              ),
                            });
                          }}
                        />
                      ) : (
                        <ViewEditCardHoverIcon
                          previousValue={selectedPreviousAddress?.postalCode}
                        />
                      ))}
                  </ValueColumn>
                </FieldsetRow>
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>District</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>
                      {shouldShowEditButtons &&
                      (selectedPreviousAddress?.district?.name !==
                        selectedAddress?.district?.name ||
                        selectedPreviousAddress?.districtName !==
                          selectedAddress?.districtName) &&
                      isApprover
                        ? selectedPreviousAddress?.district?.name ??
                          selectedPreviousAddress?.districtName
                        : selectedAddress?.district?.name ??
                          selectedAddress?.districtName}
                    </ValueLabel>
                    {shouldShowEditButtons &&
                      (selectedPreviousAddress?.district?.name !==
                        selectedAddress?.district?.name ||
                        selectedPreviousAddress?.districtName !==
                          selectedAddress?.districtName) &&
                      (isApprover ? (
                        <ApproveEditCardHoverIcon
                          newValue={
                            selectedAddress?.district?.name ??
                            selectedAddress?.districtName
                          }
                          onConfirm={async () => {
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "DistrictId",
                              })
                            );
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "DistrictName",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              employeePreviousValues:
                                localProfile.employeePreviousValues
                                  ? {
                                      ...localProfile.employeePreviousValues,
                                      addresses:
                                        localProfile.employeePreviousValues.addresses?.map(
                                          (addr) =>
                                            addr.purpose?.identifier ===
                                            selectedAddress?.purpose?.identifier
                                              ? {
                                                  ...addr,
                                                  district:
                                                    selectedAddress?.district,
                                                  districtName:
                                                    selectedAddress?.districtName,
                                                }
                                              : addr
                                        ),
                                    }
                                  : null,
                            });
                          }}
                          onCancel={async () => {
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "DistrictId",
                              })
                            );
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "DistrictName",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              addresses: localProfile.addresses.map((addr) =>
                                addr.purpose?.identifier ===
                                selectedAddress?.purpose?.identifier
                                  ? {
                                      ...addr,
                                      district:
                                        selectedPreviousAddress!.district,
                                      districtName:
                                        selectedPreviousAddress!.districtName,
                                    }
                                  : addr
                              ),
                            });
                          }}
                        />
                      ) : (
                        <ViewEditCardHoverIcon
                          previousValue={
                            selectedPreviousAddress?.district?.name ??
                            selectedPreviousAddress?.districtName
                          }
                        />
                      ))}
                  </ValueColumn>
                </FieldsetRow>
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>Municipality</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>
                      {shouldShowEditButtons &&
                      (selectedPreviousAddress?.municipality?.name !==
                        selectedAddress?.municipality?.name ||
                        selectedPreviousAddress?.municipalityName !==
                          selectedAddress?.municipalityName) &&
                      isApprover
                        ? selectedPreviousAddress?.municipality?.name ??
                          selectedPreviousAddress?.municipalityName
                        : selectedAddress?.municipality?.name ??
                          selectedAddress?.municipalityName}
                    </ValueLabel>
                    {shouldShowEditButtons &&
                      (selectedPreviousAddress?.municipality?.name !==
                        selectedAddress?.municipality?.name ||
                        selectedPreviousAddress?.municipalityName !==
                          selectedAddress?.municipalityName) &&
                      (isApprover ? (
                        <ApproveEditCardHoverIcon
                          newValue={
                            selectedAddress?.municipality?.name ??
                            selectedAddress?.municipalityName
                          }
                          onConfirm={async () => {
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "MunicipalityId",
                              })
                            );
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "MunicipalityName",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              employeePreviousValues:
                                localProfile.employeePreviousValues
                                  ? {
                                      ...localProfile.employeePreviousValues,
                                      addresses:
                                        localProfile.employeePreviousValues.addresses?.map(
                                          (addr) =>
                                            addr.purpose?.identifier ===
                                            selectedAddress?.purpose?.identifier
                                              ? {
                                                  ...addr,
                                                  municipality:
                                                    selectedAddress?.municipality,
                                                  municipalityName:
                                                    selectedAddress?.municipalityName,
                                                }
                                              : addr
                                        ),
                                    }
                                  : null,
                            });
                          }}
                          onCancel={async () => {
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "MunicipalityId",
                              })
                            );
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "MunicipalityName",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              addresses: localProfile.addresses.map((addr) =>
                                addr.purpose?.identifier ===
                                selectedAddress?.purpose?.identifier
                                  ? {
                                      ...addr,
                                      municipality:
                                        selectedPreviousAddress!.municipality,
                                      municipalityName:
                                        selectedPreviousAddress!
                                          .municipalityName,
                                    }
                                  : addr
                              ),
                            });
                          }}
                        />
                      ) : (
                        <ViewEditCardHoverIcon
                          previousValue={
                            selectedPreviousAddress?.municipality?.name ??
                            selectedPreviousAddress?.municipalityName
                          }
                        />
                      ))}
                  </ValueColumn>
                </FieldsetRow>
                <FieldsetRow>
                  <LabelColumn>
                    <LightLabel>Address</LightLabel>
                  </LabelColumn>
                  <ValueColumn>
                    <ValueLabel>
                      {shouldShowEditButtons &&
                      formatFullAddress(selectedPreviousAddress) !==
                        formatFullAddress(selectedAddress) &&
                      isApprover
                        ? formatFullAddress(selectedPreviousAddress)
                        : formatFullAddress(selectedAddress)}
                    </ValueLabel>
                    {shouldShowEditButtons &&
                      formatFullAddress(selectedPreviousAddress) !==
                        formatFullAddress(selectedAddress) &&
                      (isApprover ? (
                        <ApproveEditCardHoverIcon
                          newValue={formatFullAddress(selectedAddress)}
                          onConfirm={async () => {
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "Street",
                              })
                            );
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "Block",
                              })
                            );
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "Apartment",
                              })
                            );
                            await approveEmployeeEdit(
                              new ApproveEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "Neighborhood",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              employeePreviousValues:
                                localProfile.employeePreviousValues
                                  ? {
                                      ...localProfile.employeePreviousValues,
                                      addresses:
                                        localProfile.employeePreviousValues.addresses?.map(
                                          (addr) =>
                                            addr.purpose?.identifier ===
                                            selectedAddress?.purpose?.identifier
                                              ? {
                                                  ...addr,
                                                  street:
                                                    selectedAddress?.street,
                                                  block: selectedAddress?.block,
                                                  apartment:
                                                    selectedAddress?.apartment,
                                                  neighborhood:
                                                    selectedAddress?.neighborhood,
                                                }
                                              : addr
                                        ),
                                    }
                                  : null,
                            });
                          }}
                          onCancel={async () => {
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "Street",
                              })
                            );
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "Block",
                              })
                            );
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "Apartment",
                              })
                            );
                            await declineEmployeeEdit(
                              new DeclineEmployeeEditDTO({
                                employeeId: employeeId,
                                collectionName: "Addresses",
                                collectionItemId: selectedAddress?.id,
                                propertyName: "Neighborhood",
                              })
                            );
                            setLocalProfile({
                              ...localProfile,
                              addresses: localProfile.addresses.map((addr) =>
                                addr.purpose?.identifier ===
                                selectedAddress?.purpose?.identifier
                                  ? {
                                      ...addr,
                                      street: selectedPreviousAddress!.street,
                                      block: selectedPreviousAddress!.block,
                                      apartment:
                                        selectedPreviousAddress!.apartment,
                                      neighborhood:
                                        selectedPreviousAddress!.neighborhood,
                                    }
                                  : addr
                              ),
                            });
                          }}
                        />
                      ) : (
                        <ViewEditCardHoverIcon
                          previousValue={formatFullAddress(
                            selectedPreviousAddress
                          )}
                        />
                      ))}
                  </ValueColumn>
                </FieldsetRow>
              </StyledFieldset>
            </LeftContainer>
            <RightContainersWrapper>
              <RightContainer>
                <StyledFieldset
                  onSubmit={(e) => e.preventDefault()}
                  data-testid="permanent-address-fieldset"
                >
                  <Legend data-testid="information-legend">Phone number</Legend>
                  <EditButton
                    data-testid="edit-address-button"
                    normalImage={editIcon}
                    hoverImage={editIconHover}
                    onClick={() => handleEditAddress()}
                    label=""
                    isdisabled={true}
                  />
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Personal phone</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {shouldShowEditButtons &&
                        selectedPreviousAddress?.phone !==
                          selectedAddress?.phone &&
                        isApprover
                          ? selectedPreviousAddress?.phone
                          : selectedAddress?.phone}
                      </ValueLabel>
                      {shouldShowEditButtons &&
                        selectedPreviousAddress?.phone !==
                          selectedAddress?.phone &&
                        (isApprover ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.phone}
                            onConfirm={async () => {
                              await approveEmployeeEdit(
                                new ApproveEmployeeEditDTO({
                                  employeeId: employeeId,
                                  collectionName: "Addresses",
                                  collectionItemId: selectedAddress?.id,
                                  propertyName: "Phone",
                                })
                              );
                              setLocalProfile({
                                ...localProfile,
                                employeePreviousValues:
                                  localProfile.employeePreviousValues
                                    ? {
                                        ...localProfile.employeePreviousValues,
                                        addresses:
                                          localProfile.employeePreviousValues.addresses?.map(
                                            (addr) =>
                                              addr.purpose?.identifier ===
                                              selectedAddress?.purpose
                                                ?.identifier
                                                ? {
                                                    ...addr,
                                                    phone:
                                                      selectedAddress?.phone,
                                                  }
                                                : addr
                                          ),
                                      }
                                    : null,
                              });
                            }}
                            onCancel={async () => {
                              await declineEmployeeEdit(
                                new DeclineEmployeeEditDTO({
                                  employeeId: employeeId,
                                  collectionName: "Addresses",
                                  collectionItemId: selectedAddress?.id,
                                  propertyName: "Phone",
                                })
                              );
                              setLocalProfile({
                                ...localProfile,
                                addresses: localProfile.addresses.map((addr) =>
                                  addr.purpose?.identifier ===
                                  selectedAddress?.purpose?.identifier
                                    ? {
                                        ...addr,
                                        phone: selectedPreviousAddress!.phone,
                                      }
                                    : addr
                                ),
                              });
                            }}
                          />
                        ) : (
                          <ViewEditCardHoverIcon
                            previousValue={selectedPreviousAddress?.phone}
                          />
                        ))}
                    </ValueColumn>
                  </FieldsetRow>
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Work phone</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {shouldShowEditButtons &&
                        selectedPreviousAddress?.workPhone !==
                          selectedAddress?.workPhone &&
                        isApprover
                          ? selectedPreviousAddress?.workPhone
                          : selectedAddress?.workPhone}
                      </ValueLabel>
                      {shouldShowEditButtons &&
                        selectedPreviousAddress?.workPhone !==
                          selectedAddress?.workPhone &&
                        (isApprover ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.workPhone}
                            onConfirm={async () => {
                              await approveEmployeeEdit(
                                new ApproveEmployeeEditDTO({
                                  employeeId: employeeId,
                                  collectionName: "Addresses",
                                  collectionItemId: selectedAddress?.id,
                                  propertyName: "WorkPhone",
                                })
                              );
                              setLocalProfile({
                                ...localProfile,
                                employeePreviousValues:
                                  localProfile.employeePreviousValues
                                    ? {
                                        ...localProfile.employeePreviousValues,
                                        addresses:
                                          localProfile.employeePreviousValues.addresses?.map(
                                            (addr) =>
                                              addr.purpose?.identifier ===
                                              selectedAddress?.purpose
                                                ?.identifier
                                                ? {
                                                    ...addr,
                                                    workPhone:
                                                      selectedAddress?.workPhone,
                                                  }
                                                : addr
                                          ),
                                      }
                                    : null,
                              });
                            }}
                            onCancel={async () => {
                              await declineEmployeeEdit(
                                new DeclineEmployeeEditDTO({
                                  employeeId: employeeId,
                                  collectionName: "Addresses",
                                  collectionItemId: selectedAddress?.id,
                                  propertyName: "WorkPhone",
                                })
                              );
                              setLocalProfile({
                                ...localProfile,
                                addresses: localProfile.addresses.map((addr) =>
                                  addr.purpose?.identifier ===
                                  selectedAddress?.purpose?.identifier
                                    ? {
                                        ...addr,
                                        workPhone:
                                          selectedPreviousAddress!.workPhone,
                                      }
                                    : addr
                                ),
                              });
                            }}
                          />
                        ) : (
                          <ViewEditCardHoverIcon
                            previousValue={selectedPreviousAddress?.workPhone}
                          />
                        ))}
                    </ValueColumn>
                  </FieldsetRow>
                </StyledFieldset>
              </RightContainer>
              <RightContainer>
                <StyledFieldset
                  onSubmit={(e) => e.preventDefault()}
                  data-testid="permanent-address-fieldset"
                >
                  <Legend data-testid="information-legend">E-mail</Legend>
                  <EditButton
                    data-testid="edit-address-button"
                    normalImage={editIcon}
                    hoverImage={editIconHover}
                    onClick={() => handleEditAddress()}
                    label=""
                    isdisabled={true}
                  />
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Personal e-mail</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {shouldShowEditButtons &&
                        selectedPreviousAddress?.email !==
                          selectedAddress?.email &&
                        isApprover
                          ? selectedPreviousAddress?.email
                          : selectedAddress?.email}
                      </ValueLabel>
                      {shouldShowEditButtons &&
                        selectedPreviousAddress?.email !==
                          selectedAddress?.email &&
                        (isApprover ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.email}
                            onConfirm={async () => {
                              await approveEmployeeEdit(
                                new ApproveEmployeeEditDTO({
                                  employeeId: employeeId,
                                  collectionName: "Addresses",
                                  collectionItemId: selectedAddress?.id,
                                  propertyName: "Email",
                                })
                              );
                              setLocalProfile({
                                ...localProfile,
                                employeePreviousValues:
                                  localProfile.employeePreviousValues
                                    ? {
                                        ...localProfile.employeePreviousValues,
                                        addresses:
                                          localProfile.employeePreviousValues.addresses?.map(
                                            (addr) =>
                                              addr.purpose?.identifier ===
                                              selectedAddress?.purpose
                                                ?.identifier
                                                ? {
                                                    ...addr,
                                                    email:
                                                      selectedAddress?.email,
                                                  }
                                                : addr
                                          ),
                                      }
                                    : null,
                              });
                            }}
                            onCancel={async () => {
                              await declineEmployeeEdit(
                                new DeclineEmployeeEditDTO({
                                  employeeId: employeeId,
                                  collectionName: "Addresses",
                                  collectionItemId: selectedAddress?.id,
                                  propertyName: "Email",
                                })
                              );
                              setLocalProfile({
                                ...localProfile,
                                addresses: localProfile.addresses.map((addr) =>
                                  addr.purpose?.identifier ===
                                  selectedAddress?.purpose?.identifier
                                    ? {
                                        ...addr,
                                        email: selectedPreviousAddress!.email,
                                      }
                                    : addr
                                ),
                              });
                            }}
                          />
                        ) : (
                          <ViewEditCardHoverIcon
                            previousValue={selectedPreviousAddress?.email}
                          />
                        ))}
                    </ValueColumn>
                  </FieldsetRow>
                  <FieldsetRow>
                    <LabelColumn>
                      <LightLabel>Work e-mail</LightLabel>
                    </LabelColumn>
                    <ValueColumn>
                      <ValueLabel>
                        {shouldShowEditButtons &&
                        selectedPreviousAddress?.workEmail !==
                          selectedAddress?.workEmail &&
                        isApprover
                          ? selectedPreviousAddress?.workEmail
                          : selectedAddress?.workEmail}
                      </ValueLabel>
                      {shouldShowEditButtons &&
                        selectedPreviousAddress?.workEmail !==
                          selectedAddress?.workEmail &&
                        (isApprover ? (
                          <ApproveEditCardHoverIcon
                            newValue={selectedAddress?.workEmail}
                            onConfirm={async () => {
                              await approveEmployeeEdit(
                                new ApproveEmployeeEditDTO({
                                  employeeId: employeeId,
                                  collectionName: "Addresses",
                                  collectionItemId: selectedAddress?.id,
                                  propertyName: "WorkEmail",
                                })
                              );
                              setLocalProfile({
                                ...localProfile,
                                employeePreviousValues:
                                  localProfile.employeePreviousValues
                                    ? {
                                        ...localProfile.employeePreviousValues,
                                        addresses:
                                          localProfile.employeePreviousValues.addresses?.map(
                                            (addr) =>
                                              addr.purpose?.identifier ===
                                              selectedAddress?.purpose
                                                ?.identifier
                                                ? {
                                                    ...addr,
                                                    workEmail:
                                                      selectedAddress?.workEmail,
                                                  }
                                                : addr
                                          ),
                                      }
                                    : null,
                              });
                            }}
                            onCancel={async () => {
                              await declineEmployeeEdit(
                                new DeclineEmployeeEditDTO({
                                  employeeId: employeeId,
                                  collectionName: "Addresses",
                                  collectionItemId: selectedAddress?.id,
                                  propertyName: "WorkEmail",
                                })
                              );
                              setLocalProfile({
                                ...localProfile,
                                addresses: localProfile.addresses.map((addr) =>
                                  addr.purpose?.identifier ===
                                  selectedAddress?.purpose?.identifier
                                    ? {
                                        ...addr,
                                        workEmail:
                                          selectedPreviousAddress!.workEmail,
                                      }
                                    : addr
                                ),
                              });
                            }}
                          />
                        ) : (
                          <ViewEditCardHoverIcon
                            previousValue={selectedPreviousAddress?.workEmail}
                          />
                        ))}
                    </ValueColumn>
                  </FieldsetRow>
                </StyledFieldset>
              </RightContainer>
            </RightContainersWrapper>
          </MainContent>
        </MainContentWrapper>
        <RightColumn>
          {addresses &&
            addresses.map((address, index) => (
              <MenuItem
                key={index}
                onClick={() => handleAddressSelect(address)}
              >
                <MenuText isSelected={selectedAddress === address}>
                  {getAddressPurposeDescription(address.purpose.identifier)}
                </MenuText>
                <MenuLine />
              </MenuItem>
            ))}
          <MenuItem onClick={() => handleAddNewAddress()}>
            <MenuText>New address</MenuText>
          </MenuItem>
        </RightColumn>
      </ContainersWrapper>
    </WrapperContainer>
  );
};

export default Addresses;
