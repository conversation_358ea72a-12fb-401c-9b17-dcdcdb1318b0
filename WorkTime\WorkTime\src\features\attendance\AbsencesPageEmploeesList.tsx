import React, { useState, useEffect } from "react";
import styled from "styled-components";
import Container from "../../components/Container";
import Avatar from "../../components/CalendarComponent/Avatar";
import Label from "../../components/Inputs/Label";
import { generateColorFromName } from "../../utils/colorUtils";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO";
import { Employee } from "./useFilteredEmployees";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { onPayrollsLoaded, selectPayrolls } from "../payroll/payrollsActions";
import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";
import { PayrollDTO } from "../../models/DTOs/payrolls/PayrollDTO";

const AbsencesPageEmployeesListContainer = styled(Container)`
  display: block;
`;

const TableContainer = styled(Container)`
  background-color: var(--attendancies-right-view-table-background-color);
  border-radius: 1.8rem;
  padding-bottom: 1rem;
  overflow-y: auto;
`;

const TableHeaderRow = styled(Container)`
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
`;

const CardContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  height: 15rem;
  overflow-y: scroll;
  scrollbar-color: var(--attendancies-hover-emploeeys)
    var(--attendancies-right-view-table-background-color);
`;

const TableRow = styled(Container)<{ isSelected: boolean; isHovered: boolean }>`
  display: grid;
  grid-template-columns: 1.8rem 16rem 6rem;
  align-items: center;
  cursor: pointer;
  padding-left: 0.3rem;
  flex-shrink: 0;
  height: 1.5rem;
  column-gap: 0.2rem;
  margin: 0.1rem 0rem;
  padding-left: 0.5rem;
  background-color: ${(props) =>
    props.isSelected
      ? "var(--attendancies-selected-emploee)"
      : "var(--attendancies-right-view-table-background-color)"};

  background-color: ${(props) =>
    props.isHovered
      ? "var(--attendancies-hover-emploeeys)"
      : "var(--attendancies-right-view-table-background-color)"};
`;

interface LabelProps {
  boldness?: number;
  fontSize?: number;
  color?: string;
  justifyContent?: string;
  align?: string;
  onClick?: () => void;
}

const StyledAvatar = styled(Avatar)`
  margin-right: 0.5rem;
`;

const StyledLabel = styled(Label)<LabelProps>`
  text-align: ${(props) => props.align || "center"};
  color: ${(props) => props.color || "inherit"};
  font-weight: ${(props) => props.boldness || "normal"};
  font-size: ${(props) => (props.fontSize ? `${props.fontSize}px` : "inherit")};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

interface ExtendedEmployee extends LightPayrollDTO {
  isSelected: boolean;
  isHovered: boolean;
}

interface AbsencesPageEmployeesListProps {
  selectedEmployee: Employee | undefined;
  onSelectEmployee: (employee: Employee | undefined) => void;
  selectedPayroll: LightPayrollDTO | undefined;
}

const TabLabel = styled(Label)`
  font-size: 1rem;
  font-weight: 500;
  color: var(--attendancies-right-view-active-tab-label-font-color);
`;

export const AbsencesPageEmployeesList: React.FC<
  AbsencesPageEmployeesListProps
> = ({ selectedEmployee, onSelectEmployee, selectedPayroll }) => {
  const [extendedEmployees, setExtendedEmployees] = useState<
    ExtendedEmployee[]
  >([]);

  const dispatch = useAppDispatch();
  const payrolls = useAppSelector(selectPayrolls).payrolls;

  useEffect(() => {
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

    if (!payrolls || payrolls.length === 0) {
      dispatch(onPayrollsLoaded(companyId));
    }
  }, [dispatch]);

  useEffect(() => {
    const initializedEmployees = payrolls
      .filter(
        (payroll) =>
          payroll.structureLevelId === selectedPayroll?.structureLevelId
      )
      .map((payroll) => ({
        ...payroll,
        firstName: payroll.employee.firstName,
        midleName: payroll.employee.secondName,
        lastName: payroll.employee.lastName,
        position: payroll.position,
        isSelected: false,
        isHovered: false,
      }));
    setExtendedEmployees(initializedEmployees);
  }, [payrolls]);

  const handleRowSelection = (id: string) => {
    setExtendedEmployees((prevEmployees) =>
      prevEmployees.map((employee) =>
        employee.id === id
          ? { ...employee, isSelected: !employee.isSelected }
          : { ...employee, isSelected: false }
      )
    );

    if (selectedEmployee && selectedEmployee.id === id) {
      onSelectEmployee(undefined);
    } else if (selectedEmployee) {
      onSelectEmployee(selectedEmployee);
    }
  };

  const handleEmployeeHover = (id: string | null) => {
    setExtendedEmployees((prevEmployees) =>
      prevEmployees.map((employee) => ({
        ...employee,
        isHovered: employee.id === id,
      }))
    );
  };

  return (
    <AbsencesPageEmployeesListContainer data-testid="absences-page-employees-list-container">
      <TableContainer data-testid="table-container">
        <TableHeaderRow data-testid="table-header-row">
          <TabLabel children="DEPARTMENT" />
        </TableHeaderRow>
        <CardContainer>
          {extendedEmployees.map((employee: ExtendedEmployee) => (
            <TableRow
              key={employee.id}
              isSelected={employee.isSelected}
              isHovered={employee.isHovered}
              onClick={() => handleRowSelection(employee.id)}
              onMouseEnter={() => handleEmployeeHover(employee.id)}
              onMouseLeave={() => handleEmployeeHover(null)}
              data-testid={`table-row-${employee.id}`}
            >
              <StyledAvatar
                name={`${employee.employee.firstName} ${employee.employee.lastName}`}
                photo={""}
                size={1}
                isVisible={true}
                background={generateColorFromName(
                  `${employee.employee.firstName} ${employee.employee.lastName}`
                )}
                data-testid={`styled-avatar-${employee.id}`}
              />
              <StyledLabel
                children={`${employee.employee.firstName} ${employee.employee.lastName}`}
                boldness={450}
                fontSize={15}
                align="left"
                data-testid={`styled-label-${employee.id}`}
              />
              <StyledLabel
                children={`${employee.position.name} `}
                boldness={450}
                fontSize={15}
                align="left"
                color="var(--attendancies-right-view-position-font-color)"
                data-testid={`styled-label-position-${employee.id}`}
              ></StyledLabel>
            </TableRow>
          ))}
        </CardContainer>
      </TableContainer>
    </AbsencesPageEmployeesListContainer>
  );
};

export default AbsencesPageEmployeesList;
