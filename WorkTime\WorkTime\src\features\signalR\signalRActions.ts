import { AppDispatch } from "../../app/store";
import { AbsenceHospitalDTO } from "../../models/DTOs/absence/AbsenceHospitalDTO";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import {
  onPayrollAbsenceUpdated,
  onPayrollsAdded,
} from "../payroll/payrollsActions";
import { onPendingEmployeesUpdated } from "../employees/employeesActions";
import { onNotificationsAdded } from "../notifications/notificationsActions";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO";

export const handleSignalREvent = (
  eventName: string,
  data: any,
  dispatch: AppDispatch
) => {
  switch (eventName) {
    case "Notifications.Absences.Added.Push": {
      dispatch(onNotificationsAdded([data]));
      if (data.payload && Array.isArray(data.payload)) {
        try {
          const absences = data.payload as AbsenceHospitalDTO[];
          absences.map((absence) => dispatch(onPayrollAbsenceUpdated(absence)));
        } catch (e) {
          console.error("Failed to parse absence payload:", e);
        }
      }
      break;
    }
    case "Notifications.Absences.Updated.Push": {
      dispatch(onNotificationsAdded([data]));
      if (data.payload) {
        try {
          const absence = data.payload as AbsenceHospitalDTO;
          dispatch(onPayrollAbsenceUpdated(absence));
        } catch (e) {
          console.error("Failed to parse updated absence payload:", e);
        }
      }
      break;
    }
    case "Notifications.Absences.Updated.PushMany": {
      if (data.payload) {
        try {
          const absences = data.payload as AbsenceHospitalDTO[];
          for (const absence of absences) {
            dispatch(onPayrollAbsenceUpdated(absence));
          }
        } catch (e) {
          console.error("Failed to parse updated absence payload:", e);
        }
      }
      break;
    }
    case "Notifications.PendingEmployees.Updated.Push":
      dispatch(onPendingEmployeesUpdated(data as EmployeePayrollDTO[]));
      break;
    case "Notifications.Absences.EditedByAdmin.Push":
      dispatch(onNotificationsAdded([data]));
      if (data.payload) {
        try {
          const absence = data.payload as AbsenceHospitalDTO;
          dispatch(onPayrollAbsenceUpdated(absence));
        } catch (e) {
          console.error("Failed to parse absence payload:", e);
        }
      }
      break;
    case "Notifications.Payrolls.Added.Push":
      dispatch(onPayrollsAdded(data.payload as LightPayrollDTO[]));
      break;
    default:
      console.warn("Unhandled SignalR event:", eventName, data);
      break;
  }
};
