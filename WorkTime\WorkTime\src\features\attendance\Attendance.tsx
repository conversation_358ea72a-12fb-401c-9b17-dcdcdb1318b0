import React, { useEffect, useState } from "react";
import { styled } from "styled-components";
import Container from "../../components/Container";
import MainWindowContainer from "../../components/MainWindowContainer";
import AttendancesRightView from "./AttendancesRightView";
import DatesTableContainer from "./DatesTableContainer";
import { Employee } from "./useFilteredEmployees";
import { useSearchParams } from "react-router-dom";
import {
  LightPayrollDTO,
  mapPayrollToLightPayroll,
} from "../../models/DTOs/payrolls/LightPayrollDTO";
import { useUserEmployee } from "../UserEmployeeContext";
import { DefaultPermissions } from "../../constants/permissions";

const FileContainer = styled(Container)`
  display: flex;
  z-index: 1;
  width: 100%;

  @media (max-width: 950px) {
    flex-direction: column;
    align-items: center;
    top: 0;
  }
`;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  top: 5em;
  margin: 0 1em 0 1em;
  width: 75%;

  @media (max-width: 950px) {
    order: 1;
    width: auto;
  }
`;

const RightContainer = styled(Container)`
  display: block;
  margin: 0 3em;
  border-radius: 2.2rem;
  width: 25%;

  @media (max-width: 950px) {
    order: 2;
    margin: 6em 0em;
    width: auto;
  }
`;

const Attendance: React.FC = () => {
  const [selectedEmployee, setSelectedEmployee] = useState<
    Employee | undefined
  >(undefined);
  const [selectedPayroll, setSelectedPayroll] = useState<
    LightPayrollDTO | undefined
  >(undefined);

  const [hoveredEmployee, setHoveredEmployee] = useState<Employee | undefined>(
    undefined
  );
  const [showMyAbsences, setShowMyAbsences] = useState(false);

  const [searchParams] = useSearchParams();
  const dateParam = searchParams.get("date");
  const absenceIdParam = searchParams.get("absenceId");

  const [preselectedMonth, setPreselectedMonth] = useState<number | undefined>(
    undefined
  );
  const [preselectedYear, setPreselectedYear] = useState<number | undefined>(
    undefined
  );

  const [currentYear, setCurrentYear] = useState<number>(
    new Date().getFullYear()
  );
  const [currentMonth, setCurrentMonth] = useState<number>(
    new Date().getMonth()
  );
  const [selectedMonth, setSelectedMonth] = useState<number>(
    preselectedMonth !== undefined ? preselectedMonth : currentMonth
  );
  const [selectedYear, setSelectedYear] = useState<number>(
    preselectedYear !== undefined ? preselectedYear : currentYear
  );
  const { userEmployee } = useUserEmployee();

  useEffect(() => {
    const fullDate = new Date();

    setCurrentYear(fullDate.getFullYear());
    setCurrentMonth(fullDate.getMonth());
  }, []);

  useEffect(() => {
    if (userEmployee.payrolls.length > 0) {
      setSelectedPayroll(mapPayrollToLightPayroll(userEmployee.payrolls[0]));
    }
  }, [userEmployee.payrolls]);

  useEffect(() => {
    setSelectedMonth(preselectedMonth ?? currentMonth);
    setSelectedYear(preselectedYear ?? currentYear);
  }, [preselectedMonth, preselectedYear]);

  useEffect(() => {
    if (dateParam) {
      const [month, year] = dateParam.split(".");
      setPreselectedMonth(Number(month) - 1);
      setPreselectedYear(Number(year));
    }
  }, [searchParams]);

  const handleSelectEmployee = (employee: Employee | undefined) => {
    setSelectedEmployee(employee);
    setSelectedPayroll(employee?.payrolls[0]);
  };

  const onToggleMyAbsences = () => {
    const oldValue = showMyAbsences;
    setShowMyAbsences(!oldValue);

    if (
      !userEmployee.permissions.includes(DefaultPermissions.Attendances.Write)
    )
      return;

    if (!oldValue) {
      setSelectedPayroll(mapPayrollToLightPayroll(userEmployee.payrolls[0]));
    } else {
      setSelectedPayroll(selectedEmployee?.payrolls[0]);
    }
  };

  const onSelectPayroll = (payroll: LightPayrollDTO | undefined) => {
    setSelectedPayroll(payroll);
  };

  return (
    <MainWindowContainer data-testid="main-window-container">
      <FileContainer data-testid="file-container">
        <LeftContainer data-testid="left-container">
          <DatesTableContainer
            selectedPayroll={selectedPayroll}
            setSelectedPayroll={onSelectPayroll}
            selectedEmployee={selectedEmployee}
            hoveredEmployee={hoveredEmployee}
            showMyAbsences={showMyAbsences}
            selectedMonth={selectedMonth}
            selectedYear={selectedYear}
            setSelectedMonth={setSelectedMonth}
            setSelectedYear={setSelectedYear}
            highlightedAbsenceId={absenceIdParam ?? undefined}
            data-testid="dates-table-container"
          />
        </LeftContainer>
        <RightContainer data-testid="right-container">
          <AttendancesRightView
            selectedPayroll={selectedPayroll}
            selectedEmployee={selectedEmployee}
            selectedYear={selectedYear}
            selectedMonth={selectedMonth}
            onSelectEmployee={handleSelectEmployee}
            hoveredEmployee={hoveredEmployee}
            onEmployeeHover={setHoveredEmployee}
            showMyAbsences={showMyAbsences}
            onToggleMyAbsences={onToggleMyAbsences}
          />
        </RightContainer>
      </FileContainer>
    </MainWindowContainer>
  );
};

export default Attendance;
